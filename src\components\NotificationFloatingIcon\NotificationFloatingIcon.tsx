import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { BellIcon } from "@heroicons/react/24/outline";

interface NotificationFloatingIconProps {
  unreadCount?: number;
}

const NotificationFloatingIcon: React.FC<NotificationFloatingIconProps> = ({
  unreadCount = 0,
}) => {
  const navigate = useNavigate();
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = () => {
    navigate("/member/notifications");
  };

  return (
    <div className="fixed top-20 right-6 z-50">
      <button
        onClick={handleClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        className={`
          relative flex items-center gap-3 px-4 py-3
          bg-[#1e3a5f] text-white rounded-lg shadow-lg
          hover:bg-[#2a4a6b] transition-all duration-200
          transform hover:scale-105 focus:outline-none focus:ring-2
          focus:ring-[#1e3a5f] focus:ring-offset-2
          ${isHovered ? "shadow-xl" : "shadow-lg"}
        `}
        aria-label={`Notifications${unreadCount > 0 ? ` (${unreadCount} unread)` : ""}`}
      >
        <BellIcon className="h-5 w-5" />
        <span className="text-sm font-medium">Notifications</span>

        {/* Unread count badge */}
        {unreadCount > 0 && (
          <span className="absolute -top-2 -right-2 bg-[#E63946] text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center shadow-sm">
            {unreadCount > 99 ? "99+" : unreadCount}
          </span>
        )}

        {/* Pulse animation for unread notifications */}
        {unreadCount > 0 && (
          <span className="absolute inset-0 rounded-lg bg-[#1e3a5f] animate-ping opacity-20"></span>
        )}
      </button>
    </div>
  );
};

export default NotificationFloatingIcon;
