import React, { useEffect, useState, Suspense, lazy } from "react";
import { MemberWrapper } from "../../../components/MemberWrapper";
import { MkdLoader } from "../../../components/MkdLoader";
import { Skeleton } from "../../../components/Skeleton";
import { PaginationBar } from "../../../components/PaginationBar";
import { Link } from "react-router-dom";
import { StarIcon } from "../../../assets/svgs";
import SearchIcon from "../../../assets/svgs/SearchIcon";

// Lazy load the DeliveryCalculator to improve initial page load performance
const DeliveryCalculator = lazy(() =>
  import("../../../components/DeliveryCalculator").then((module) => ({
    default: module.DeliveryCalculator,
  }))
);
import {
  useMarketplaceListingsQuery,
  useFeaturedListingsQuery,
  useToggleFavoriteMutation,
  useAvailableLocationsQuery,
  usePrimaryLocationQuery,
  IMarketplaceFilters,
} from "../../../query/useMarketplace";

interface ISeller {
  id: number;
  name: string;
  email: string;
  data: {
    first_name: string;
    last_name: string;
    phone: string;
    referral_code: string | null;
    referral_type: string;
    terms_accepted: boolean;
    privacy_accepted: boolean;
    referrer_name: string;
  };
  status: number;
  verify: number;
  rating: number;
  location: string;
  createdAt: string;
}

interface IListing {
  id: number;
  name: string;
  seller: ISeller;
  price: string;
  discountPrice?: string;
  type: string;
  category: string;
  status: string;
  description?: string;
  image?: string;
  images: string[];
  location: string;
  rating: number;
  sponsored: boolean;
  viewCount: number;
  favoriteCount: number;
  isFavorited: boolean;
  createdAt: string;
  updatedAt: string;
}

interface IPagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Helper functions moved outside component to prevent recreation on every render
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

const renderStars = (rating: number) => {
  return Array.from({ length: 5 }, (_, index) => (
    <StarIcon
      key={index}
      className={`w-4 h-4 ${
        index < Math.floor(rating) ? "text-yellow-400" : "text-gray-300"
      }`}
    />
  ));
};

// Memoized Skeleton Component
const ListingSkeleton = React.memo(() => (
  <div className="bg-white rounded-lg overflow-hidden shadow-lg">
    <Skeleton className="h-48 w-full" />
    <div className="p-4">
      <Skeleton className="h-4 w-3/4 mb-2" />
      <Skeleton className="h-6 w-1/2 mb-2" />
      <Skeleton className="h-3 w-full mb-3" />
      <div className="flex justify-between items-center">
        <Skeleton className="h-4 w-20" />
        <Skeleton className="h-4 w-24" />
      </div>
    </div>
  </div>
));

ListingSkeleton.displayName = "ListingSkeleton";

const MemberMarketplaceListPage = React.memo(() => {
  const [filters, setFilters] = useState<IMarketplaceFilters>({
    page: 1,
    limit: 12,
    search: "",
    category: "",
    location: "",
    type: "",
    sort: "Recently Added",
    minPrice: undefined,
    maxPrice: undefined,
    sponsoredOnly: false,
    myLocationOnly: false,
    myOffersOnly: false,
    myFavoritesOnly: false,
  });
  const [searchInput, setSearchInput] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [minPriceInput, setMinPriceInput] = useState("");
  const [maxPriceInput, setMaxPriceInput] = useState("");

  // Single state to manage which calculator is open (only one at a time)
  const [openCalculatorId, setOpenCalculatorId] = useState<number | null>(null);

  // Close calculator when page changes to prevent memory leaks
  useEffect(() => {
    setOpenCalculatorId(null);
  }, [filters.page]);

  // Queries
  const {
    data: listings,
    isLoading: listingsLoading,
    error: listingsError,
    refetch: refetchListings,
  } = useMarketplaceListingsQuery(filters);

  const {
    data: featuredListings,
    isLoading: featuredLoading,
  } = useFeaturedListingsQuery();

  const {
    data: availableLocations,
    isLoading: locationsLoading,
  } = useAvailableLocationsQuery();

  const {
    data: primaryLocation,
    isLoading: primaryLocationLoading,
  } = usePrimaryLocationQuery();

  const toggleFavoriteMutation = useToggleFavoriteMutation();

  // Extract pagination data
  const pagination = (listings as any)?.pagination;

  // Create skeleton array for loading states
  const listingsSkeletons = Array.from({ length: 8 }, (_, index) => (
    <ListingSkeleton key={index} />
  ));

  // Event handlers
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value);
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setFilters((prev) => ({ ...prev, search: searchInput, page: 1 }));
  };

  const handleCategoryFilter = (category: string) => {
    setFilters((prev) => ({ ...prev, category, page: 1 }));
  };

  const handleLocationFilter = (location: string) => {
    setFilters((prev) => ({ ...prev, location, page: 1 }));
  };

  const handleTypeFilter = (type: string) => {
    setFilters((prev) => ({ ...prev, type, page: 1 }));
  };

  const handleSortChange = (sort: string) => {
    setFilters((prev) => ({ ...prev, sort, page: 1 }));
  };

  const handlePriceRangeChange = () => {
    const minPrice = minPriceInput ? parseFloat(minPriceInput) : undefined;
    const maxPrice = maxPriceInput ? parseFloat(maxPriceInput) : undefined;
    setFilters((prev) => ({
      ...prev,
      minPrice,
      maxPrice,
      page: 1,
    }));
  };

  const handleCheckboxChange = (
    key: keyof IMarketplaceFilters,
    value: boolean
  ) => {
    setFilters((prev) => ({ ...prev, [key]: value, page: 1 }));
  };

  const handlePageChange = (page: number) => {
    setFilters((prev) => ({ ...prev, page }));
  };

  const handleToggleFavorite = (listingId: number, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    toggleFavoriteMutation.mutate(listingId);
  };

  const handleCalculatorToggle = (listingId: number, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setOpenCalculatorId(openCalculatorId === listingId ? null : listingId);
  };

  const handleClearFilters = () => {
    setFilters({
      page: 1,
      limit: 12,
      search: "",
      category: "",
      location: "",
      type: "",
      sort: "Recently Added",
      minPrice: undefined,
      maxPrice: undefined,
      sponsoredOnly: false,
      myLocationOnly: false,
      myOffersOnly: false,
      myFavoritesOnly: false,
    });
    setSearchInput("");
    setMinPriceInput("");
    setMaxPriceInput("");
  };

  return (
    <MemberWrapper>
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-blue-900 p-4">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-white text-3xl font-bold mb-2">
              Marketplace
            </h1>
            <p className="text-gray-300">
              Discover amazing products and services from our community
            </p>
          </div>

          {/* Search and Filters */}
          <div className="mb-8">
            {/* Search Bar */}
            <form onSubmit={handleSearchSubmit} className="mb-6">
              <div className="relative">
                <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search listings..."
                  value={searchInput}
                  onChange={handleSearchChange}
                  className="w-full pl-10 pr-4 py-3 bg-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                />
                <button
                  type="submit"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-600 text-white px-4 py-1 rounded-md hover:bg-blue-700 transition-colors"
                >
                  Search
                </button>
              </div>
            </form>

            {/* Filter Toggle */}
            <div className="flex items-center justify-between mb-4">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2 px-4 py-2 bg-white text-gray-700 rounded-lg hover:bg-gray-50 text-sm font-medium"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
                  />
                </svg>
                Filters
              </button>

              <button className="flex items-center gap-2 px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 text-sm font-medium">
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"
                  />
                </svg>
                Sort
              </button>

              <button className="p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 10h16M4 14h16M4 18h16"
                  />
                </svg>
              </button>

              <button className="p-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200">
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                  />
                </svg>
              </button>
            </div>

            {/* Filter Row */}
            <div className="grid grid-cols-4 gap-4 mb-6">
              {/* Category */}
              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Category
                </label>
                <select
                  value={filters.category}
                  onChange={(e) => handleCategoryFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm bg-white"
                >
                  <option value="">All Categories</option>
                  <option value="Electronics">Electronics</option>
                  <option value="Services">Services</option>
                  <option value="Fashion">Fashion</option>
                  <option value="Home">Home</option>
                  <option value="Automotive">Automotive</option>
                  <option value="Sports">Sports</option>
                  <option value="Books">Books</option>
                  <option value="Health">Health</option>
                </select>
              </div>

              {/* Type */}
              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Type
                </label>
                <select
                  value={filters.type}
                  onChange={(e) => handleTypeFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm bg-white"
                >
                  <option value="">All Types</option>
                  <option value="Product">Product</option>
                  <option value="Service">Service</option>
                </select>
              </div>

              {/* Location */}
              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Location
                </label>
                <select
                  value={filters.location}
                  onChange={(e) => handleLocationFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm bg-white"
                >
                  <option value="">All Locations</option>
                  {(availableLocations as any)?.map((location: string) => (
                    <option key={location} value={location}>
                      {location}
                    </option>
                  ))}
                </select>
              </div>

              {/* Sort */}
              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Sort By
                </label>
                <select
                  value={filters.sort}
                  onChange={(e) => handleSortChange(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm bg-white"
                >
                  <option value="Recently Added">Recently Added</option>
                  <option value="Price: Low to High">Price: Low to High</option>
                  <option value="Price: High to Low">Price: High to Low</option>
                  <option value="Most Popular">Most Popular</option>
                </select>
              </div>
            </div>

            {/* Price Range */}
            <div className="grid grid-cols-2 gap-4 mb-6">
              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Min Price
                </label>
                <input
                  type="number"
                  placeholder="0"
                  value={minPriceInput}
                  onChange={(e) => setMinPriceInput(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm bg-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Max Price
                </label>
                <input
                  type="number"
                  placeholder="1000"
                  value={maxPriceInput}
                  onChange={(e) => setMaxPriceInput(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm bg-white"
                />
              </div>
            </div>

            {/* Checkboxes */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <label className="flex items-center text-white text-sm">
                <input
                  type="checkbox"
                  checked={filters.sponsoredOnly}
                  onChange={(e) =>
                    handleCheckboxChange("sponsoredOnly", e.target.checked)
                  }
                  className="mr-2"
                />
                Sponsored Only
              </label>
              <label className="flex items-center text-white text-sm">
                <input
                  type="checkbox"
                  checked={filters.myLocationOnly}
                  onChange={(e) =>
                    handleCheckboxChange("myLocationOnly", e.target.checked)
                  }
                  className="mr-2"
                />
                My Location Only
              </label>
              <label className="flex items-center text-white text-sm">
                <input
                  type="checkbox"
                  checked={filters.myOffersOnly}
                  onChange={(e) =>
                    handleCheckboxChange("myOffersOnly", e.target.checked)
                  }
                  className="mr-2"
                />
                My Offers Only
              </label>
              <label className="flex items-center text-white text-sm">
                <input
                  type="checkbox"
                  checked={filters.myFavoritesOnly}
                  onChange={(e) =>
                    handleCheckboxChange("myFavoritesOnly", e.target.checked)
                  }
                  className="mr-2"
                />
                My Favorites Only
              </label>
            </div>

            {/* Apply and Clear Buttons */}
            <div className="flex gap-4">
              <button
                onClick={handlePriceRangeChange}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Apply Filters
              </button>
              <button
                onClick={handleClearFilters}
                className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                Clear All
              </button>
            </div>
          </div>

          {/* Featured Listings */}
          {featuredListings && (featuredListings as any).length > 0 && (
            <div className="mb-8">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-white text-xl font-semibold">
                  Featured Listings
                </h2>
                                  <span className="text-gray-300 text-sm">
                    {featuredLoading ? "Loading..." : `${(featuredListings as any)?.length || 0} featured`}
                  </span>
              </div>

              {featuredLoading ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {listingsSkeletons}
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {(featuredListings as any).map((listing: IListing) => (
                    <div
                      key={listing.id}
                      className="bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow"
                    >
                      <Link to={`/member/marketplace/${listing.id}`}>
                        {/* Image */}
                        <div className="relative h-48 bg-gray-200">
                          {listing.image ? (
                            <img
                              src={listing.image}
                              alt={listing.name}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center text-gray-400">
                              <svg
                                className="w-12 h-12"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                                  clipRule="evenodd"
                                />
                              </svg>
                            </div>
                          )}
                          {listing.sponsored && (
                            <div className="absolute top-2 left-2 bg-yellow-400 text-yellow-900 px-2 py-1 rounded text-xs font-medium">
                              Sponsored
                            </div>
                          )}
                          <button
                            onClick={(e) => handleToggleFavorite(listing.id, e)}
                            className="absolute top-2 right-2 p-1 bg-white rounded-full shadow-md hover:bg-gray-50"
                          >
                            <svg
                              className={`w-5 h-5 ${
                                listing.isFavorited
                                  ? "text-red-500 fill-current"
                                  : "text-gray-400"
                              }`}
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                              />
                            </svg>
                          </button>
                        </div>

                        {/* Content */}
                        <div className="p-4">
                          <h3 className="font-semibold text-gray-900 mb-2 text-base">
                            {listing.name}
                          </h3>
                          <div className="flex items-center gap-2 mb-3">
                            <p className="text-[#E63946] font-bold text-lg">
                              eBa$ {listing.price}
                            </p>
                            {listing.discountPrice && (
                              <p className="text-gray-500 text-sm line-through">
                                eBa$ {listing.discountPrice}
                              </p>
                            )}
                          </div>

                          <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                            {listing.description}
                          </p>

                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-1">
                              {renderStars(listing.seller?.rating || 0)}
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="w-5 h-5 bg-gray-300 rounded-full flex items-center justify-center">
                                <span className="text-xs text-gray-600 font-medium">
                                  {listing.seller?.name?.charAt(0)?.toUpperCase() ||
                                    "U"}
                                </span>
                              </div>
                              <span className="text-xs text-gray-600">
                                {listing.seller?.name || "Unknown Seller"}
                              </span>
                            </div>
                          </div>

                          <div className="flex justify-between items-center">
                            <span className="text-xs text-gray-500 flex items-center gap-1">
                              <svg
                                className="w-3 h-3"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                                  clipRule="evenodd"
                                />
                              </svg>
                              {listing.location}
                            </span>
                            <span className="text-xs text-gray-500">
                              Added {formatDate(listing.createdAt)}
                            </span>
                          </div>
                        </div>
                      </Link>

                      {/* Delivery Calculator Button - Outside Link */}
                      {/* <div className="px-4 pb-4">
                      <button
                        onClick={(e) => handleCalculatorToggle(listing.id, e)}
                        className="w-full bg-blue-50 hover:bg-blue-100 text-blue-600 py-2 px-3 rounded-md text-sm font-medium transition-colors"
                      >
                        {openCalculatorId === listing.id ? "Hide" : "Calculate"}{" "}
                        Delivery & Fees
                      </button>
                    </div> */}

                      {/* Delivery Calculator */}
                      {openCalculatorId === listing.id && (
                        <div className="border-t border-gray-200 p-4 bg-gray-50">
                          <Suspense
                            fallback={<Skeleton className="h-32 w-full" />}
                          >
                            <DeliveryCalculator
                              listingId={listing.id}
                              listingPrice={parseFloat(listing.price)}
                              listingCurrency="eBa$"
                              onCalculationComplete={(result) => {
                                console.log(
                                  "Delivery calculation result:",
                                  result
                                );
                              }}
                            />
                          </Suspense>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ) : null}

          {/* All Listings */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-white text-xl font-semibold">All Listings</h2>
              <span className="text-gray-300 text-sm">
                {listingsLoading
                  ? "Loading..."
                  : pagination
                    ? `Showing ${(pagination.page - 1) * pagination.limit + 1} to ${Math.min(pagination.page * pagination.limit, pagination.total)} of ${pagination.total} listings`
                    : "No listings found"}
              </span>
            </div>

            {listingsLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {listingsSkeletons}
              </div>
            ) : listingsError ? (
              <div className="text-center py-8">
                <p className="text-gray-300 mb-4">Failed to load listings</p>
                <button
                  onClick={() => refetchListings()}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Try Again
                </button>
              </div>
            ) : listings && listings.data && listings.data.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {listings.data.map((listing: IListing) => (
                  <div
                    key={listing.id}
                    className="bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow"
                  >
                    <Link to={`/member/marketplace/${listing.id}`}>
                      {/* Image */}
                      <div className="relative h-48 bg-gray-200">
                        {listing.image ? (
                          <img
                            src={listing.image}
                            alt={listing.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center text-gray-400">
                            <svg
                              className="w-12 h-12"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </div>
                        )}
                        {listing.sponsored && (
                          <div className="absolute top-2 left-2 bg-yellow-400 text-yellow-900 px-2 py-1 rounded text-xs font-medium">
                            Sponsored
                          </div>
                        )}
                        <button
                          onClick={(e) => handleToggleFavorite(listing.id, e)}
                          className="absolute top-2 right-2 p-1 bg-white rounded-full shadow-md hover:bg-gray-50"
                        >
                          <svg
                            className={`w-5 h-5 ${
                              listing.isFavorited
                                ? "text-red-500 fill-current"
                                : "text-gray-400"
                            }`}
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                            />
                          </svg>
                        </button>
                      </div>

                      {/* Content */}
                      <div className="p-4">
                        <h3 className="font-semibold text-gray-900 mb-2 text-base">
                          {listing.name}
                        </h3>
                        <div className="flex items-center gap-2 mb-3">
                          <p className="text-[#E63946] font-bold text-lg">
                            eBa$ {listing.price}
                          </p>
                          {listing.discountPrice && (
                            <p className="text-gray-500 text-sm line-through">
                              eBa$ {listing.discountPrice}
                            </p>
                          )}
                        </div>

                        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                          {listing.description}
                        </p>

                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-1">
                            {renderStars(listing.seller?.rating || 0)}
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-5 h-5 bg-gray-300 rounded-full flex items-center justify-center">
                              <span className="text-xs text-gray-600 font-medium">
                                {listing.seller?.name?.charAt(0)?.toUpperCase() ||
                                  "U"}
                              </span>
                            </div>
                            <span className="text-xs text-gray-600">
                              {listing.seller?.name || "Unknown Seller"}
                            </span>
                          </div>
                        </div>

                        <div className="flex justify-between items-center">
                          <span className="text-xs text-gray-500 flex items-center gap-1">
                            <svg
                              className="w-3 h-3"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                                clipRule="evenodd"
                              />
                            </svg>
                            {listing.location}
                          </span>
                          <span className="text-xs text-gray-500">
                            Added {formatDate(listing.createdAt)}
                          </span>
                        </div>
                      </div>
                    </Link>

                    {/* Delivery Calculator Button - Outside Link */}
                    {/* <div className="px-4 pb-4">
                      <button
                        onClick={(e) => handleCalculatorToggle(listing.id, e)}
                        className="w-full bg-blue-50 hover:bg-blue-100 text-blue-600 py-2 px-3 rounded-md text-sm font-medium transition-colors"
                      >
                        {openCalculatorId === listing.id ? "Hide" : "Calculate"}{" "}
                        Delivery & Fees
                      </button>
                    </div> */}

                    {/* Delivery Calculator */}
                    {openCalculatorId === listing.id && (
                      <div className="border-t border-gray-200 p-4 bg-gray-50">
                        <Suspense
                          fallback={<Skeleton className="h-32 w-full" />}
                        >
                          <DeliveryCalculator
                            listingId={listing.id}
                            listingPrice={parseFloat(listing.price)}
                            listingCurrency="eBa$"
                            onCalculationComplete={(result) => {
                              console.log(
                                "Delivery calculation result:",
                                result
                              );
                            }}
                          />
                        </Suspense>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-300 mb-4">No listings found</p>
                <button
                  onClick={handleClearFilters}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Clear Filters
                </button>
              </div>
            )}
          </div>

          {/* Pagination */}
          {pagination && pagination.totalPages > 1 && (
            <div className="flex justify-center mt-8">
              <div className="flex items-center gap-2">
                {/* Previous Button */}
                <button
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={!pagination.hasPrev}
                  className="text-white hover:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 19l-7-7 7-7"
                    />
                  </svg>
                </button>

                {/* Page Numbers */}
                <div className="flex items-center gap-1">
                  {Array.from(
                    { length: Math.min(5, pagination.totalPages) },
                    (_, i) => {
                      const pageNum = i + 1;
                      return (
                        <button
                          key={pageNum}
                          onClick={() => handlePageChange(pageNum)}
                          className={`px-3 py-1 rounded ${
                            pageNum === pagination.page
                              ? "bg-blue-600 text-white"
                              : "text-white hover:text-gray-300"
                          }`}
                        >
                          {pageNum}
                        </button>
                      );
                    }
                  )}
                </div>

                {/* Next Button */}
                <button
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={!pagination.hasNext}
                  className="text-white hover:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </MemberWrapper>
  );
});

MemberMarketplaceListPage.displayName = "MemberMarketplaceListPage";

export default MemberMarketplaceListPage;
