import * as yup from "yup";
import { useState, useEffect, useCallback } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { InteractiveButton } from "@/components/InteractiveButton";
import { useToast } from "@/hooks/useToast";
import { RoleEnum, ToastStatusEnum } from "@/utils/Enums";
import { LazyLoad } from "@/components/LazyLoad";
import { useSDK } from "@/hooks/useSDK";
import { MkdPasswordInput } from "@/components/MkdPasswordInput";
import MkdInputV2 from "@/components/MkdInputV2";
import { useUpdateModelMutation } from "@/pages/Member/updateModel";
import { Models } from "@/utils/baas";
import { useContexts } from "@/hooks/useContexts";
import mockData from "@/utils/mockData/memberSignup.json";

interface MemberSignUpPageProps {
  role?: string;
}

const MemberSignUpPage = ({
  role: _role = RoleEnum.USER,
}: MemberSignUpPageProps) => {
  const { sdk } = useSDK();
  const { success, error: showError } = useToast();
  const { authDispatch: dispatch, showToast } = useContexts();
  const { mutateAsync: updateModel } = useUpdateModelMutation(
    Models.USER,
    "member"
  );
  const [searchParams] = useSearchParams();

  const [submitLoading, setSubmitLoading] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);
  const [referralType, setReferralType] = useState<"code" | "link" | "none">(
    "none"
  );
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [privacyAccepted, setPrivacyAccepted] = useState(false);
  const [referralCodeValid, setReferralCodeValid] = useState<boolean | null>(
    null
  );
  const [referralCodeValidating, setReferralCodeValidating] = useState(false);
  const [referrerName, setReferrerName] = useState("");

  // New state variables for two-phase signup
  const [registeredUser, setRegisteredUser] = useState<any>(null);
  const [profileUpdated, setProfileUpdated] = useState(false);
  const [skipToDashboard, setSkipToDashboard] = useState(false);

  const navigate = useNavigate();

  const schema = yup
    .object({
      fullName: yup.string().required("Full name is required"),
      email: yup.string().email("Invalid email").required("Email is required"),
      phoneNumber: yup.string().required("Phone number is required"),
      password: yup
        .string()
        .min(8, "Password must be at least 8 characters")
        .required("Password is required"),
      referralCode: yup.string().when("referralType", {
        is: "code",
        then: (schema) => schema.required("Referral code is required"),
        otherwise: (schema) => schema.notRequired(),
      }),
    })
    .required();

  const {
    register,
    handleSubmit,
    setError,
    setValue,
    watch,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const handlePrefill = () => {
    const randomUser = mockData[Math.floor(Math.random() * mockData.length)];
    const randomSuffix = Math.floor(Math.random() * 1000);
    setValue("fullName", randomUser.fullName);
    setValue("email", `${randomUser.email}+${randomSuffix}@example.com`);
    setValue("phoneNumber", randomUser.phoneNumber);
    setValue("password", randomUser.password);

    if (randomUser.referralCode) {
      setReferralType("code");
      setValue("referralCode", randomUser.referralCode);
    } else {
      const referralTypes = ["link", "none"];
      const randomReferralType = referralTypes[
        Math.floor(Math.random() * referralTypes.length)
      ] as "link" | "none";
      setReferralType(randomReferralType);
      if (randomReferralType === "link") {
        setValue("referralCode", "REFLINK123");
        setReferrerName("Referred by Link");
      } else {
        setValue("referralCode", "");
      }
    }

    setTermsAccepted(true);
    setPrivacyAccepted(true);
  };

  const watchedReferralCode = watch("referralCode");
  const memberData = watch();
  const { fullName, phoneNumber } = memberData;

  // Check for referral code in URL parameters
  useEffect(() => {
    const refParam = searchParams.get("ref");
    if (refParam) {
      setReferralType("link");
      setValue("referralCode", refParam);
      validateReferralCode(refParam);
    }
  }, [searchParams, setValue]);

  // Validate referral code when it changes
  useEffect(() => {
    if (
      referralType === "code" &&
      watchedReferralCode &&
      watchedReferralCode.length >= 6
    ) {
      validateReferralCode(watchedReferralCode);
    } else if (!watchedReferralCode) {
      setReferralCodeValid(null);
      setReferrerName("");
    }
  }, [watchedReferralCode, referralType]);

  const validateReferralCode = useCallback(
    async (code: string) => {
      if (!code || code.length < 6) return;

      setReferralCodeValidating(true);
      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/signup/validate-referral",
          method: "POST",
          body: { referralCode: code },
        });

        console.log("Validate Referral Code API Response:", response);

        if (!response.error) {
          setReferralCodeValid(true);
          setReferrerName(String(response.data?.referrerName || ""));
        } else {
          setReferralCodeValid(false);
          setReferrerName("");
          setError("referralCode", {
            message: String(response.message || "Invalid referral code"),
          });
        }
      } catch (error: any) {
        console.error("Error validating referral code:", error);
        setReferralCodeValid(false);
        setReferrerName("");
        setError("referralCode", {
          message: String(error?.message || "Failed to validate referral code"),
        });
      } finally {
        setReferralCodeValidating(false);
      }
    },
    [setError]
  );

  // Profile update function (similar to trainer signup)
  const updateProfile = async () => {
    try {
      // Split full name into first and last name
      const nameParts = fullName.trim().split(" ");
      const firstName = nameParts[0];
      const lastName = nameParts.slice(1).join(" ") || "";

      await updateModel({
        id: registeredUser?.user_id || 11,
        payload: {
          verify: true,
          data: JSON.stringify({
            first_name: firstName,
            last_name: lastName,
            phone: phoneNumber,
            referral_code: referralType !== "none" ? watchedReferralCode : null,
            referral_type: referralType,
            terms_accepted: termsAccepted,
            privacy_accepted: privacyAccepted,
            referrer_name: referrerName,
          }),
        },
      });
      setProfileUpdated(true);
    } catch (e: any) {
      showToast("Profile update failed", 4000, ToastStatusEnum.ERROR);
      setSubmitLoading(false);
      console.error("Profile update error:", e);
    }
  };

  const testProfile = () => {
    updateProfile();
  };

  // Effect: after LOGIN, update profile
  useEffect(() => {
    if (registeredUser && registeredUser.user_id && !profileUpdated) {
      updateProfile();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [registeredUser, updateModel, profileUpdated]);

  // Effect: after profile update, navigate
  useEffect(() => {
    if (profileUpdated) {
      if (skipToDashboard) {
        success("Account created successfully! Redirecting to dashboard...");
        navigate("/member/dashboard");
      } else {
        success("Account created successfully! Redirecting to verification...");
        navigate("/member/verify-identity");
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [profileUpdated, skipToDashboard]);

  // Update loading state when profile is being updated
  useEffect(() => {
    if (registeredUser && !profileUpdated) {
      setSubmitLoading(true);
    } else if (profileUpdated) {
      setSubmitLoading(false);
    }
  }, [registeredUser, profileUpdated]);

  const onSubmit = useCallback(
    async (data: yup.InferType<typeof schema>) => {
      if (!termsAccepted) {
        showError("Please accept the terms of service");
        return;
      }

      if (!privacyAccepted) {
        showError("Please accept the privacy policy");
        return;
      }

      // Set the navigation preference to go to verification
      setSkipToDashboard(false);

      // Validate referral code if provided
      // if (
      //   referralType === "code" &&
      //   data.referralCode &&
      //   referralCodeValid !== true
      // ) {
      //   showError("Please enter a valid referral code");
      //   return;
      // }

      try {
        setSubmitLoading(true);
        setApiError(null);

        // Phase 1: Register user using SDK register method
        const result: any = await sdk.register(
          data.email,
          data.password,
          "member"
        );

        if (!result.error) {
          setRegisteredUser(result); // Save for useEffect
          dispatch({ type: "LOGIN", payload: result as any });
          showToast("Successfully Registered", 4000, ToastStatusEnum.SUCCESS);
        } else {
          setSubmitLoading(false);
          if (result.validation) {
            const keys = Object.keys(result.validation);
            for (let i = 0; i < keys.length; i++) {
              const field = keys[i];
              setError(field as any, {
                type: "manual",
                message: result.validation[field],
              });
            }
          } else {
            setApiError(String(result.message || "Failed to create account"));
            showError(String(result.message || "Failed to create account"));
          }
        }
      } catch (error: any) {
        console.error("Error during signup:", error);
        const errorMessage = String(
          error?.response?.data?.message || error?.message || "Signup failed"
        );
        setApiError(errorMessage);
        showError(errorMessage);
        setSubmitLoading(false);
      }
    },
    [
      termsAccepted,
      privacyAccepted,
      referralType,
      referralCodeValid,

      dispatch,
      showToast,
      showError,
      setError,
    ]
  );

  const onSubmitSkipToDashboard = useCallback(
    async (data: yup.InferType<typeof schema>) => {
      if (!termsAccepted) {
        showError("Please accept the terms of service");
        return;
      }

      if (!privacyAccepted) {
        showError("Please accept the privacy policy");
        return;
      }

      // Set the navigation preference to go to dashboard
      setSkipToDashboard(true);

      // Validate referral code if provided
      // if (
      //   referralType === "code" &&
      //   data.referralCode &&
      //   referralCodeValid !== true
      // ) {
      //   showError("Please enter a valid referral code");
      //   return;
      // }

      try {
        setSubmitLoading(true);
        setApiError(null);

        // Phase 1: Register user using SDK register method
        const result: any = await sdk.register(
          data.email,
          data.password,
          "member"
        );

        if (!result.error) {
          setRegisteredUser(result); // Save for useEffect
          dispatch({ type: "LOGIN", payload: result as any });
          showToast("Successfully Registered", 4000, ToastStatusEnum.SUCCESS);
        } else {
          setSubmitLoading(false);
          if (result.validation) {
            const keys = Object.keys(result.validation);
            for (let i = 0; i < keys.length; i++) {
              const field = keys[i];
              setError(field as any, {
                type: "manual",
                message: result.validation[field],
              });
            }
          } else {
            setApiError(String(result.message || "Failed to create account"));
            showError(String(result.message || "Failed to create account"));
          }
        }
      } catch (error: any) {
        console.error("Error during signup:", error);
        const errorMessage = String(
          error?.response?.data?.message || error?.message || "Signup failed"
        );
        setApiError(errorMessage);
        showError(errorMessage);
        setSubmitLoading(false);
      }
    },
    [
      termsAccepted,
      privacyAccepted,
      referralType,
      referralCodeValid,

      dispatch,
      showToast,
      showError,
      setError,
    ]
  );

  return (
    <main className="flex h-screen">
      {/* Left Sidebar */}
      <div className="w-[250px] bg-[#0F2C59] flex flex-col">
        {/* Logo */}
        <div className="p-6 border-b border-gray-600">
          <h1 className="text-2xl font-bold space-x-1">
            <span style={{ color: "#E63946" }}>eBa</span>
            <span className="text-white">Dollar</span>
          </h1>
        </div>

        {/* Progress Steps */}
        <div className="p-6 flex-1">
          <div className="space-y-6">
            {/* Basic Information Step */}
            <div className="flex items-center space-x-3">
              <div className="w-6 h-6 rounded-full bg-[#E63946] flex items-center justify-center">
                {/* <span className="text-white text-xs font-bold">1</span> */}
              </div>
              <div>
                <div className="text-white font-medium">Basic Information</div>
                {/* <div className="text-gray-300 text-sm">50% Complete</div> */}
              </div>
            </div>

            {/* Verify Identity Step */}
            <div className="flex items-center space-x-3">
              <div className="w-6 h-6 rounded-full border-2 border-gray-500 flex items-center justify-center">
                {/* <span className="text-gray-500 text-xs font-bold">2</span> */}
              </div>
              <div>
                <div className="text-gray-500 font-medium">Verify Identity</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Content Area */}
      <div className="flex-1 bg-white flex items-start justify-center p-8 h-full overflow-y-auto ">
        <div className="w-full max-w-2xl">
          <div className="mb-8">
            <h2 className="text-4xl font-bold text-[#1A202C] mb-2">
              Create your account
            </h2>
            <p className="text-[#6B7280] text-base">
              Get started now and complete the remaining steps later.
            </p>
          </div>

          {/* Error Display */}
          {apiError && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
              <div className="flex items-center">
                <div className="text-red-400 mr-3">
                  <svg
                    className="w-5 h-5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-red-800">
                    Signup Failed
                  </h3>
                  <p className="text-sm text-red-700 mt-1">{apiError}</p>
                </div>
              </div>
            </div>
          )}

          <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
            {/* Progress Bar */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  <div className="text-red-400 mr-3">
                    <svg
                      width="62"
                      height="62"
                      viewBox="0 0 62 62"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g filter="url(#filter0_dd_318_1464)">
                        <path
                          d="M15 21C15 12.1634 22.1634 5 31 5C39.8366 5 47 12.1634 47 21C47 29.8366 39.8366 37 31 37C22.1634 37 15 29.8366 15 21Z"
                          fill="#F52D2A"
                        />
                        <path
                          d="M37.125 31H24.875V11H37.125V31Z"
                          stroke="#E5E7EB"
                        />
                        <g clip-path="url(#clip0_318_1464)">
                          <path
                            d="M31 20.75C31.9283 20.75 32.8185 20.3813 33.4749 19.7249C34.1313 19.0685 34.5 18.1783 34.5 17.25C34.5 16.3217 34.1313 15.4315 33.4749 14.7751C32.8185 14.1187 31.9283 13.75 31 13.75C30.0717 13.75 29.1815 14.1187 28.5251 14.7751C27.8687 15.4315 27.5 16.3217 27.5 17.25C27.5 18.1783 27.8687 19.0685 28.5251 19.7249C29.1815 20.3813 30.0717 20.75 31 20.75ZM29.7504 22.0625C27.057 22.0625 24.875 24.2445 24.875 26.9379C24.875 27.3863 25.2387 27.75 25.6871 27.75H36.3129C36.7613 27.75 37.125 27.3863 37.125 26.9379C37.125 24.2445 34.943 22.0625 32.2496 22.0625H29.7504Z"
                            fill="white"
                          />
                        </g>
                      </g>
                      <defs>
                        <filter
                          id="filter0_dd_318_1464"
                          x="0"
                          y="0"
                          width="62"
                          height="62"
                          filterUnits="userSpaceOnUse"
                          color-interpolation-filters="sRGB"
                        >
                          <feFlood
                            flood-opacity="0"
                            result="BackgroundImageFix"
                          />
                          <feColorMatrix
                            in="SourceAlpha"
                            type="matrix"
                            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                            result="hardAlpha"
                          />
                          <feOffset dy="10" />
                          <feGaussianBlur stdDeviation="7.5" />
                          <feColorMatrix
                            type="matrix"
                            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"
                          />
                          <feBlend
                            mode="normal"
                            in2="BackgroundImageFix"
                            result="effect1_dropShadow_318_1464"
                          />
                          <feColorMatrix
                            in="SourceAlpha"
                            type="matrix"
                            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                            result="hardAlpha"
                          />
                          <feOffset dy="4" />
                          <feGaussianBlur stdDeviation="3" />
                          <feColorMatrix
                            type="matrix"
                            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"
                          />
                          <feBlend
                            mode="normal"
                            in2="effect1_dropShadow_318_1464"
                            result="effect2_dropShadow_318_1464"
                          />
                          <feBlend
                            mode="normal"
                            in="SourceGraphic"
                            in2="effect2_dropShadow_318_1464"
                            result="shape"
                          />
                        </filter>
                        <clipPath id="clip0_318_1464">
                          <path
                            d="M24.875 13.75H37.125V27.75H24.875V13.75Z"
                            fill="white"
                          />
                        </clipPath>
                      </defs>
                    </svg>
                  </div>
                  <div className="flex flex-col ">
                    <span className="text-base font-semibold text-[#1A202C]">
                      Basic Information
                    </span>
                    <span className="text-sm text-[#6B7280]">Step 1 of 2</span>
                  </div>
                </div>

                <span className="text-sm font-medium text-[#E63946]">
                  {profileUpdated ? "100% Complete" : "0% Complete"}
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-[#E63946] h-2 rounded-full transition-all duration-300"
                  style={{ width: profileUpdated ? "100%" : "0%" }}
                ></div>
              </div>
            </div>
            <div className="flex items-center justify-between mb-2">
              <div className="flex flex-col items-center">
                <div className="flex items-center bg-[#E63946] rounded-full w-2 h-2"></div>
                <div className="text-sm font-medium text-gray-700 mb-3">
                  Basic Info
                </div>
              </div>

              <div className="flex flex-col items-center">
                <div className="flex items-center bg-[#D1D5DB] rounded-full w-2 h-2"></div>
                <div className="text-xs font-medium text-[#9CA3AF] mb-3">
                  Verify Identity
                </div>
              </div>
            </div>

            {/* Basic Info Section */}
            <div className="space-y-4">
              {/* Full Name */}
              <div>
                <LazyLoad>
                  <MkdInputV2
                    name="fullName"
                    type="text"
                    register={register}
                    errors={errors}
                    required
                    placeholder="Enter your full name"
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label className="text-sm font-medium text-gray-700">
                        Full Name
                      </MkdInputV2.Label>
                      <MkdInputV2.Field className="!bg-white !border-gray-300 !text-gray-900 focus:!border-[#E63946] focus:!ring-[#E63946]" />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </LazyLoad>
              </div>

              {/* Email Address */}
              <div>
                <LazyLoad>
                  <MkdInputV2
                    name="email"
                    type="email"
                    register={register}
                    errors={errors}
                    required
                    placeholder="<EMAIL>"
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label className="text-sm font-medium text-gray-700">
                        Email Address
                      </MkdInputV2.Label>
                      <MkdInputV2.Field className="!bg-white !border-gray-300 !text-gray-900 focus:!border-[#E63946] focus:!ring-[#E63946]" />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </LazyLoad>
              </div>

              {/* Phone Number */}
              <div>
                <LazyLoad>
                  <MkdInputV2
                    name="phoneNumber"
                    type="tel"
                    register={register}
                    errors={errors}
                    required
                    placeholder="(*************"
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label className="text-sm font-medium text-gray-700">
                        Phone Number
                      </MkdInputV2.Label>
                      <MkdInputV2.Field className="!bg-white !border-gray-300 !text-gray-900 focus:!border-[#E63946] focus:!ring-[#E63946]" />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </LazyLoad>
              </div>

              {/* Password */}
              <div>
                <LazyLoad>
                  <MkdPasswordInput
                    required
                    name="password"
                    label="Password"
                    errors={errors}
                    register={register}
                    inputClassName="!bg-white !border-gray-300 !text-gray-900 focus:!border-[#E63946] focus:!ring-[#E63946]"
                    placeholder="Create a strong password"
                    labelClassName="text-sm font-medium text-gray-700"
                  />
                </LazyLoad>
              </div>

              <p className="text-xs text-gray-500">
                Must be at least 8 characters with uppercase, lowercase and
                numbers
              </p>
            </div>

            {/* Referral Information */}
            <div className="bg-blue-50 rounded-lg p-4 space-y-4">
              <div className="flex items-center space-x-4">
                <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs h-8 w-8">
                    <svg
                      width="32"
                      height="32"
                      viewBox="0 0 32 32"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M16 0C24.8366 0 32 7.16344 32 16C32 24.8366 24.8366 32 16 32C7.16344 32 0 24.8366 0 16C0 7.16344 7.16344 0 16 0Z"
                        fill="#DBEAFE"
                      />
                      <path
                        d="M16 0C24.8366 0 32 7.16344 32 16C32 24.8366 24.8366 32 16 32C7.16344 32 0 24.8366 0 16C0 7.16344 7.16344 0 16 0Z"
                        stroke="#E5E7EB"
                      />
                      <path d="M24.75 26H7.25V6H24.75V26Z" stroke="#E5E7EB" />
                      <g clip-path="url(#clip0_318_1525)">
                        <path
                          d="M11.1875 8.75C11.7677 8.75 12.3241 8.98047 12.7343 9.3907C13.1445 9.80094 13.375 10.3573 13.375 10.9375C13.375 11.5177 13.1445 12.0741 12.7343 12.4843C12.3241 12.8945 11.7677 13.125 11.1875 13.125C10.6073 13.125 10.0509 12.8945 9.6407 12.4843C9.23047 12.0741 9 11.5177 9 10.9375C9 10.3573 9.23047 9.80094 9.6407 9.3907C10.0509 8.98047 10.6073 8.75 11.1875 8.75ZM21.25 8.75C21.8302 8.75 22.3866 8.98047 22.7968 9.3907C23.207 9.80094 23.4375 10.3573 23.4375 10.9375C23.4375 11.5177 23.207 12.0741 22.7968 12.4843C22.3866 12.8945 21.8302 13.125 21.25 13.125C20.6698 13.125 20.1134 12.8945 19.7032 12.4843C19.293 12.0741 19.0625 11.5177 19.0625 10.9375C19.0625 10.3573 19.293 9.80094 19.7032 9.3907C20.1134 8.98047 20.6698 8.75 21.25 8.75ZM7.25 16.9176C7.25 15.307 8.55703 14 10.1676 14H11.3352C11.7699 14 12.1828 14.0957 12.5547 14.2652C12.5191 14.4621 12.5027 14.6672 12.5027 14.875C12.5027 15.9195 12.9621 16.8574 13.6867 17.5C13.6812 17.5 13.6758 17.5 13.6676 17.5H7.83242C7.5125 17.5 7.25 17.2375 7.25 16.9176ZM18.3324 17.5C18.327 17.5 18.3215 17.5 18.3133 17.5C19.0406 16.8574 19.4973 15.9195 19.4973 14.875C19.4973 14.6672 19.4781 14.4648 19.4453 14.2652C19.8172 14.093 20.2301 14 20.6648 14H21.8324C23.443 14 24.75 15.307 24.75 16.9176C24.75 17.2402 24.4875 17.5 24.1676 17.5H18.3324ZM13.375 14.875C13.375 14.1788 13.6516 13.5111 14.1438 13.0188C14.6361 12.5266 15.3038 12.25 16 12.25C16.6962 12.25 17.3639 12.5266 17.8562 13.0188C18.3484 13.5111 18.625 14.1788 18.625 14.875C18.625 15.5712 18.3484 16.2389 17.8562 16.7312C17.3639 17.2234 16.6962 17.5 16 17.5C15.3038 17.5 14.6361 17.2234 14.1438 16.7312C13.6516 16.2389 13.375 15.5712 13.375 14.875ZM10.75 22.0199C10.75 20.0074 12.3824 18.375 14.3949 18.375H17.6051C19.6176 18.375 21.25 20.0074 21.25 22.0199C21.25 22.4219 20.9246 22.75 20.5199 22.75H11.4801C11.0781 22.75 10.75 22.4246 10.75 22.0199Z"
                          fill="#2563EB"
                        />
                      </g>
                      <defs>
                        <clipPath id="clip0_318_1525">
                          <path
                            d="M7.25 8.75H24.75V22.75H7.25V8.75Z"
                            fill="white"
                          />
                        </clipPath>
                      </defs>
                    </svg>
                  </span>
                </div>
                <h3 className="text-lg font-medium text-gray-900">
                  Referral Information
                </h3>
              </div>

              {/* Referral Options */}
              <div className="space-y-3  ">
                {/* I have a referral code */}
                <div className="flex items-start space-x-3 bg-white p-4 rounded-md">
                  <input
                    type="radio"
                    id="referral-code"
                    name="referralType"
                    value="code"
                    checked={referralType === "code"}
                    onChange={(e) => setReferralType(e.target.value as "code")}
                    className="mt-1 w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                  />
                  <div className="flex-1">
                    <label
                      htmlFor="referral-code"
                      className="text-sm font-medium text-gray-900 cursor-pointer"
                    >
                      I have a referral code
                    </label>
                    <p className="text-xs text-gray-600">
                      Enter the code shared by someone who referred you
                    </p>

                    {referralType === "code" && (
                      <div className="mt-2">
                        <LazyLoad>
                          <MkdInputV2
                            name="referralCode"
                            type="text"
                            register={register}
                            errors={errors}
                            placeholder="Enter referral code"
                          >
                            <MkdInputV2.Container>
                              <MkdInputV2.Field className="!bg-white !border-gray-300 !text-gray-900 focus:!border-[#E63946] focus:!ring-[#E63946]" />
                              <MkdInputV2.Error />
                            </MkdInputV2.Container>
                          </MkdInputV2>
                        </LazyLoad>

                        {/* Referral Code Validation Feedback */}
                        {watchedReferralCode &&
                          watchedReferralCode.length >= 6 && (
                            <div className="mt-2">
                              {referralCodeValidating && (
                                <div className="flex items-center text-sm text-gray-600">
                                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                                  Validating referral code...
                                </div>
                              )}
                              {!referralCodeValidating &&
                                referralCodeValid === true && (
                                  <div className="flex items-center text-sm text-green-600">
                                    <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center mr-2">
                                      <span className="text-white text-xs">
                                        ✓
                                      </span>
                                    </div>
                                    Valid referral code from {referrerName}
                                  </div>
                                )}
                              {/* {!referralCodeValidating &&
                                referralCodeValid === false && (
                                  <div className="flex items-center text-sm text-red-600">
                                    <div className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center mr-2">
                                      <span className="text-white text-xs">
                                        ✗
                                      </span>
                                    </div>
                                    Invalid referral code
                                  </div>
                                )} */}
                            </div>
                          )}
                      </div>
                    )}
                  </div>
                </div>

                {/* I clicked a referral link */}
                <div className="flex items-start space-x-3 bg-white p-4 rounded-md">
                  <input
                    type="radio"
                    id="referral-link"
                    name="referralType"
                    value="link"
                    checked={referralType === "link"}
                    onChange={(e) => setReferralType(e.target.value as "link")}
                    className="mt-1 w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                  />
                  <div className="flex-1">
                    <label
                      htmlFor="referral-link"
                      className="text-sm font-medium text-gray-900 cursor-pointer"
                    >
                      I clicked a referral link
                    </label>
                    <p className="text-xs text-gray-600">
                      The referral information has been automatically detected
                    </p>

                    {referralType === "link" && (
                      <div className="mt-2 flex items-center space-x-2">
                        <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">✓</span>
                        </div>
                        <span className="text-sm text-green-600">
                          {referrerName || "Referral detected"}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* No referral */}
                <div className="flex items-start space-x-3">
                  <input
                    type="radio"
                    id="no-referral"
                    name="referralType"
                    value="none"
                    checked={referralType === "none"}
                    onChange={(e) => setReferralType(e.target.value as "none")}
                    className="mt-1 w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                  />
                  <div className="flex-1">
                    <label
                      htmlFor="no-referral"
                      className="text-sm font-medium text-gray-900 cursor-pointer"
                    >
                      No referral
                    </label>
                    <p className="text-xs text-gray-600">
                      I found eBaDollar on my own
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-blue-100 rounded-md p-3">
                <div className="flex items-start space-x-2">
                  <div className="w-5 h-5   flex items-center justify-center mt-0.5">
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g clip-path="url(#clip0_318_1547)">
                        <g clip-path="url(#clip1_318_1547)">
                          <path
                            d="M5.95312 2.15L7.04063 4H7H4.75C4.05937 4 3.5 3.44062 3.5 2.75C3.5 2.05938 4.05937 1.5 4.75 1.5H4.81875C5.28437 1.5 5.71875 1.74688 5.95312 2.15ZM2 2.75C2 3.2 2.10938 3.625 2.3 4H1C0.446875 4 0 4.44688 0 5V7C0 7.55312 0.446875 8 1 8H15C15.5531 8 16 7.55312 16 7V5C16 4.44688 15.5531 4 15 4H13.7C13.8906 3.625 14 3.2 14 2.75C14 1.23125 12.7688 0 11.25 0H11.1812C10.1844 0 9.25938 0.528125 8.75313 1.3875L8 2.67188L7.24687 1.39062C6.74062 0.528125 5.81562 0 4.81875 0H4.75C3.23125 0 2 1.23125 2 2.75ZM12.5 2.75C12.5 3.44062 11.9406 4 11.25 4H9H8.95938L10.0469 2.15C10.2844 1.74688 10.7156 1.5 11.1812 1.5H11.25C11.9406 1.5 12.5 2.05938 12.5 2.75ZM1 9V14.5C1 15.3281 1.67188 16 2.5 16H7V9H1ZM9 16H13.5C14.3281 16 15 15.3281 15 14.5V9H9V16Z"
                            fill="#2563EB"
                          />
                        </g>
                      </g>
                      <defs>
                        <clipPath id="clip0_318_1547">
                          <rect width="16" height="16" fill="white" />
                        </clipPath>
                        <clipPath id="clip1_318_1547">
                          <path d="M0 0H16V16H0V0Z" fill="white" />
                        </clipPath>
                      </defs>
                    </svg>
                  </div>
                  <p className="text-xs text-blue-800">
                    <strong>Referral Bonus:</strong> Both you and your referrer
                    will receive rewards when you complete your first
                    transaction!
                  </p>
                </div>
              </div>
            </div>

            {/* Terms and Conditions */}
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  id="terms-privacy"
                  checked={termsAccepted && privacyAccepted}
                  onChange={(e) => {
                    setTermsAccepted(e.target.checked);
                    setPrivacyAccepted(e.target.checked);
                  }}
                  className="mt-1 w-4 h-4 text-[#E63946] border-gray-300 rounded focus:ring-[#E63946]"
                />
                <label
                  htmlFor="terms-privacy"
                  className="text-sm text-gray-700 cursor-pointer"
                >
                  I have read and accepted the{" "}
                  <Link to="/terms" className="text-[#E63946] hover:underline">
                    Terms of service
                  </Link>{" "}
                  and{" "}
                  <Link
                    to="/privacy"
                    className="text-[#E63946] hover:underline"
                  >
                    Privacy policy
                  </Link>
                </label>
              </div>
            </div>

            {/* Verification Notice */}
            <div className="bg-blue-50 rounded-md p-3">
              <div className="flex items-start space-x-2">
                <div className="w-4 h-4  rounded-full flex items-center justify-center mt-0.5">
                  <svg
                    width="12"
                    height="16"
                    viewBox="0 0 12 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g clip-path="url(#clip0_318_1562)">
                      <g clip-path="url(#clip1_318_1562)">
                        <path
                          d="M8.5 12C8.8 11.0031 9.42188 10.1531 10.0375 9.30625C10.2 9.08437 10.3625 8.8625 10.5188 8.6375C11.1375 7.74687 11.5 6.66875 11.5 5.50313C11.5 2.4625 9.0375 0 6 0C2.9625 0 0.5 2.4625 0.5 5.5C0.5 6.66563 0.8625 7.74687 1.48125 8.63437C1.6375 8.85938 1.8 9.08125 1.9625 9.30313C2.58125 10.15 3.20312 11.0031 3.5 11.9969H8.5V12ZM6 16C7.38125 16 8.5 14.8813 8.5 13.5V13H3.5V13.5C3.5 14.8813 4.61875 16 6 16ZM3.5 5.5C3.5 5.775 3.275 6 3 6C2.725 6 2.5 5.775 2.5 5.5C2.5 3.56562 4.06562 2 6 2C6.275 2 6.5 2.225 6.5 2.5C6.5 2.775 6.275 3 6 3C4.61875 3 3.5 4.11875 3.5 5.5Z"
                          fill="#3B82F6"
                        />
                      </g>
                    </g>
                    <defs>
                      <clipPath id="clip0_318_1562">
                        <rect width="12" height="16" fill="white" />
                      </clipPath>
                      <clipPath id="clip1_318_1562">
                        <path d="M0 0H12V16H0V0Z" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                </div>
                <div>
                  <p className="text-sm font-medium text-blue-900">
                    Complete verification now or later
                  </p>
                  <p className="text-xs text-blue-700">
                    You can start using the application immediately after this
                    step. We will remind you to complete identity verification
                    later.
                  </p>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-between pt-4">
              <div>
                <button
                  type="button"
                  className="flex-1 w-full max-w-[96px] px-4 py-3 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E63946]"
                  onClick={() => navigate("/member/login")}
                >
                  Cancel
                </button>
              </div>

              <div className=" flex  gap-2">
                <InteractiveButton
                  type="button"
                  className="flex-1 w-full max-w-[169px] px-4 h-[46px] border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-[#E5E7EB] hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E63946]"
                  onClick={handleSubmit(onSubmitSkipToDashboard)}
                  loading={submitLoading}
                  disabled={submitLoading}
                >
                  Skip to Main Page
                </InteractiveButton>
                {/* <InteractiveButton
                type="button"
                className="flex-1 !bg-blue-500 hover:!bg-blue-600 focus:!ring-blue-500 !py-3 !font-medium"
                onClick={handlePrefill}
              >
                Prefill Form
              </InteractiveButton> */}

                <InteractiveButton
                  type="submit"
                  className="flex-1 h-[46px] !bg-[#F52D2A] hover:!bg-[#d63384] focus:!ring-[#E63946] !py-3 !font-medium"
                  loading={submitLoading}
                  disabled={submitLoading}
                >
                  {registeredUser && !profileUpdated
                    ? "Setting up profile..."
                    : "Create Account & Continue"}
                </InteractiveButton>
              </div>
            </div>
          </form>
          {/* <p className="text-sm text-center text-gray-600 mt-6">
            Test profile
            <div
              onClick={() => {
                testProfile();
              }}
              className="font-medium text-[#E63946] hover:opacity-80 cursor-pointer"
            >
              Click me
            </div>
          </p> */}

          {/* Login Link */}
          <p className="text-sm text-center text-gray-600 mt-6">
            Already have an account?{" "}
            <Link
              to="/member/login"
              className="font-medium text-[#E63946] hover:opacity-80"
            >
              Log In
            </Link>
          </p>
        </div>
      </div>
    </main>
  );
};

export default MemberSignUpPage;
